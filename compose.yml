# Docker Compose configuration for Nepali App Microservices
# Auto-scaling and monitoring configuration

x-default-logging: &default-logging
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"

x-service-defaults: &service-defaults
  restart: unless-stopped
  networks:
    - app-network
  extra_hosts:
    - "host.docker.internal:host-gateway"
  logging: *default-logging
  deploy:
    resources:
      limits:
        cpus: '0.25'
        memory: 192M
      reservations:
        cpus: '0.05'
        memory: 64M

x-app-environment: &app-environment
  # Database Configuration - External MongoDB
  DB_URL: ${DB_URL}
  ADMIN_DB_NAME: ${ADMIN_DB_NAME}
  # Redis Configuration
  REDIS_HOST: redis
  REDIS_PORT: 6379
  REDIS_PASSWORD: ${REDIS_PASSWORD}
  # Application Configuration
  SECRET_KEY: ${SECRET_KEY}
  PROJECT_NAME: ${PROJECT_NAME}
  LOG_LEVEL: ${LOG_LEVEL}
  # API Keys
  GEMINI_API_KEY: ${GEMINI_API_KEY}
  # Force env refresh
  COMPOSE_PROJECT_NAME: ${PROJECT_NAME:-nepali-app}
  TIMESTAMP: ${TIMESTAMP:-}

x-app-healthcheck: &app-healthcheck
  test: ["CMD", "curl", "--fail", "http://localhost:8000/health"]
  interval: 60s
  timeout: 10s
  retries: 2
  start_period: 10s

services:
  # Base Image Build
  base:
    build:
      context: .
      dockerfile: docker/Dockerfile.base
    image: base:latest
    command: echo "Base image built"



  # Auth Service
  auth:
    <<: *service-defaults
    build:
      context: .
      dockerfile: docker/Dockerfile.auth
    image: auth:latest
    depends_on:
      - base
    env_file:
      - .env
    environment:
      <<: *app-environment
    volumes:
      - ./app:/app/app:ro
    healthcheck: *app-healthcheck
    labels:
      - "traefik.enable=true"
      # HTTP router - path-based routing only
      - "traefik.http.routers.auth.rule=PathPrefix(`/v1/auth`)"
      - "traefik.http.routers.auth.entrypoints=web"
      - "traefik.http.routers.auth.middlewares=cors@file,strip-auth-prefix@file"
      - "traefik.http.routers.auth.priority=100"

      # Service
      - "traefik.http.services.auth.loadbalancer.server.port=8000"

  # Socket Service
  socket:
    <<: *service-defaults
    build:
      context: .
      dockerfile: docker/Dockerfile.socket
    image: socket:latest
    depends_on:
      base:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    env_file:
      - .env
    environment:
      <<: *app-environment
      REDIS_URL: redis://redis:6379
    volumes:
      - ./app:/app/app:ro
    healthcheck: *app-healthcheck
    labels:
      - "traefik.enable=true"
      # HTTP router - path-based routing only
      - "traefik.http.routers.socket.rule=PathPrefix(`/v1/socket`)"
      - "traefik.http.routers.socket.entrypoints=web"
      - "traefik.http.routers.socket.middlewares=cors@file,strip-socket-prefix@file,audio-upload-limit@file"
      - "traefik.http.routers.socket.priority=100"

      # Service
      - "traefik.http.services.socket.loadbalancer.server.port=8000"

  # Socket Service V2 (Collection-based)
  socket_v2:
    <<: *service-defaults
    build:
      context: .
      dockerfile: docker/Dockerfile.socket_v2
    image: socket_v2:latest
    depends_on:
      base:
        condition: service_completed_successfully
      redis:
        condition: service_healthy
    env_file:
      - .env
    environment:
      <<: *app-environment
      REDIS_URL: redis://redis:6379
      SERVICE_VERSION: v2
    volumes:
      - ./app:/app/app:ro
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8002/health"]
      interval: 60s
      timeout: 10s
      retries: 2
      start_period: 10s
    labels:
      - "traefik.enable=true"
      # HTTP router - path-based routing for V2
      - "traefik.http.routers.socket_v2.rule=PathPrefix(`/v2`)"
      - "traefik.http.routers.socket_v2.entrypoints=web"
      - "traefik.http.routers.socket_v2.middlewares=cors@file,strip-v2-prefix@file,audio-upload-limit@file"
      - "traefik.http.routers.socket_v2.priority=100"

      # Service
      - "traefik.http.services.socket_v2.loadbalancer.server.port=8002"

  # Management Service (Consolidated)
  management:
    <<: *service-defaults
    build:
      context: .
      dockerfile: docker/Dockerfile.management
    image: management:latest
    depends_on:
      - base
    env_file:
      - .env
    environment:
      <<: *app-environment
    volumes:
      - ./app:/app/app:ro
    healthcheck: *app-healthcheck
    labels:
      - "traefik.enable=true"
      # HTTP router - path-based routing only
      - "traefik.http.routers.management.rule=PathPrefix(`/v1/management`)"
      - "traefik.http.routers.management.entrypoints=web"
      - "traefik.http.routers.management.middlewares=cors@file,strip-management-prefix@file"
      - "traefik.http.routers.management.priority=100"

      # Service
      - "traefik.http.services.management.loadbalancer.server.port=8000"

  # Redis Service (Socket.IO Session Store & Queue Management)
  redis:
    <<: *service-defaults
    image: redis:7.0-alpine
    volumes:
      - redis_data:/data
    env_file:
      - .env
    # Optimized for Socket.IO scaling and session management
    command: >
      redis-server
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --tcp-keepalive 60
      --timeout 300
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 15s
      timeout: 5s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.1'
          memory: 128M

  # Traefik Service (Reverse Proxy)
  traefik:
    <<: *service-defaults
    image: traefik:v2.10

    ports:
      - "0.0.0.0:${API_PORT:-8204}:80"
      - "0.0.0.0:${TRAEFIK_DASHBOARD_PORT:-8205}:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro
      - ./traefik/config:/etc/traefik/config:ro
    command:
      - "--providers.docker=true"
      - "--providers.docker.exposedByDefault=false"
      - "--entrypoints.web.address=:80"
      - "--api.dashboard=true"
      - "--api.insecure=true"
      - "--log.level=INFO"
      - "--providers.docker.network=app-network"
      - "--accesslog=true"
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 96M
        reservations:
          cpus: '0.05'
          memory: 48M



volumes:
  redis_data:
    driver: local

networks:
  app-network:
    driver: bridge
