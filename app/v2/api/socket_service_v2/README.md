# Socket Service V2 - Collection-Based Real-time Communication

Socket Service V2 is an enhanced version of the original socket service that implements collection-based storage and performance optimizations while maintaining the same Socket.IO communication flow.

## 🚀 Key Features

### Collection-Based Storage
- Returns `task_set_id` and `story_set_id` instead of individual items
- Uses existing `task_sets` and `story_steps` collections
- Groups related content under collection identifiers

### Performance Optimizations
- **Choice Questions**: `single_choice`, `multiple_choice`, `true_false` exclude media for faster processing
- **Interactive Tasks**: `speak_word`, `story_based` retain media for functionality
- Optimization statistics tracked and returned

### Same Socket.IO Flow
- Identical WebSocket communication as V1
- Same authentication and session management
- Compatible with existing client implementations

## 📡 API Endpoints

### Socket.IO Authentication
```http
POST /v1/socket_v2/connect
```
Creates an authenticated Socket.IO session and returns connection details.

### WebSocket Connection
```
/v1/socket_v2/socket.io
```
WebSocket endpoint for real-time audio streaming.

### HTTP Audio Processing (Alternative)
```http
POST /v1/socket_v2/audio/process
```
HTTP endpoint for audio processing without WebSocket.

### Story Generation
```http
POST /v1/socket_v2/story/generate
```
HTTP endpoint for story generation from audio.

### Health Check
```http
GET /v1/socket_v2/health
```
Service health status and component checks.

## 🔄 Socket.IO Communication Flow

### 1. Authentication
```javascript
// Get session token
const response = await fetch('/v1/socket_v2/connect', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <jwt_token>'
  }
});
const { session_token, websocket_url } = await response.json();
```

### 2. WebSocket Connection
```javascript
import io from 'socket.io-client';

const socket = io(websocket_url, {
  auth: {
    session_token: session_token
  }
});
```

### 3. Audio Streaming
```javascript
// Start streaming
socket.emit('stream_starting', {
  session_id: session_id,
  audio_format: 'webm'
});

// Wait for acknowledgment
socket.on('stream_starting_ack', (data) => {
  console.log('Streaming started:', data);
});

// Send audio chunks
socket.emit('binary_data', audioChunk);

// Complete streaming
socket.emit('stream_completed', {
  session_id: session_id
});
```

### 4. Receive Results
```javascript
socket.on('task_generation_complete', (data) => {
  console.log('Task Set ID:', data.task_set_id);
  console.log('Story Set ID:', data.story_set_id);
  console.log('Optimization Stats:', data.optimization_stats);
});
```

## 📊 Response Format

### V2 Collection Response
```json
{
  "task_set_id": "uuid-for-task-collection",
  "story_set_id": "uuid-for-story-collection",
  "session_id": "session-identifier",
  "status": "completed",
  "optimization_stats": {
    "total_tasks": 4,
    "media_excluded_count": 3,
    "media_included_count": 1,
    "optimization_applied": true
  },
  "processing_time": 2.5,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## 🗄️ Database Structure

### Task Sets (Existing Collection)
```javascript
{
  "_id": ObjectId,
  "user_id": "string",
  "session_id": "string", 
  "title": "string",
  "tasks": [...], // Array of task objects
  "v2_collection_id": "string", // V2 collection identifier
  "optimization_metadata": {...},
  "service_version": "v2",
  "created_at": Date
}
```

### Story Steps (Existing Collection)
```javascript
{
  "_id": ObjectId,
  "user_id": "string",
  "steps": [...], // Array of story steps
  "v2_collection_id": "string", // V2 collection identifier
  "generation_metadata": {...},
  "service_version": "v2",
  "created_at": Date
}
```

## ⚡ Performance Optimizations

### Media Exclusion Rules
- **Excluded**: `single_choice`, `multiple_choice`, `true_false`, `fill_in_blank`
- **Included**: `speak_word`, `story_based`, `audio_response`, `pronunciation`

### Optimization Benefits
- Faster task generation for choice-based questions
- Reduced bandwidth usage
- Improved response times
- Maintained functionality for interactive tasks

## 🔧 Configuration

### Environment Variables
```bash
REDIS_URL=redis://redis:6379
GEMINI_API_KEY=your_gemini_api_key
MAX_AUDIO_FILE_SIZE=209715200  # 200MB
```

### Service Configuration
```python
# File size limits
MAX_AUDIO_FILE_SIZE = 200 * 1024 * 1024  # 200MB

# Optimization settings
MEDIA_EXCLUDED_TASK_TYPES = {
    'single_choice', 'multiple_choice', 'true_false', 'fill_in_blank'
}
MEDIA_INCLUDED_TASK_TYPES = {
    'speak_word', 'story_based', 'audio_response', 'pronunciation'
}
```

## 🧪 Testing

### Run Tests
```bash
# Run all V2 tests
pytest app/v1/api/socket_service_v2/tests/ -v

# Run specific test categories
pytest app/v1/api/socket_service_v2/tests/test_service_v2.py::TestV2Models -v
pytest app/v1/api/socket_service_v2/tests/test_service_v2.py::TestV2OptimizedGeneration -v
```

### Health Check
```bash
curl http://localhost:8002/v1/socket_v2/health
```

## 🚀 Deployment

### Docker
```dockerfile
# Service runs on port 8002
EXPOSE 8002
CMD ["python", "-m", "app.v1.api.socket_service_v2"]
```

### Production Considerations
- Use Redis cluster for session management
- Configure proper CORS origins
- Set up monitoring for optimization metrics
- Monitor collection ID usage patterns

## 📈 Monitoring

### Key Metrics
- Collection creation rate
- Media optimization savings
- Processing time improvements
- Socket connection health
- Queue worker performance

### Logs
```bash
# Service logs include V2-specific information
tail -f logs/socket_service_v2.log | grep "optimization_stats"
```

## 🔄 Migration from V1

### Backward Compatibility
- Same Socket.IO events and flow
- Compatible client code
- Gradual migration possible

### Key Differences
- Response format includes collection IDs
- Performance optimizations applied
- Uses existing database collections
- Enhanced metadata tracking

## 📞 Support

For issues or questions about Socket Service V2:
1. Check the health endpoint: `/v1/socket_v2/health`
2. Review optimization statistics in responses
3. Monitor collection ID patterns
4. Verify existing collection usage
