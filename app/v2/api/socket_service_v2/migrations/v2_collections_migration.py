"""
Database Migration for Socket Service V2

This migration adds V2-specific indexes to existing task_sets and story_steps
collections to support the collection-based approach in Socket Service V2.
"""

import asyncio
import logging
from pymongo.database import Database
from app.shared.db_enums import CollectionName
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


async def ensure_existing_collections(db: Database) -> None:
    """
    Ensure existing collections are available for V2.

    Args:
        db: The MongoDB database
    """
    try:
        # Get existing collections
        existing_collections = await db.list_collection_names()

        # Verify required collections exist
        required_collections = [
            CollectionName.TASK_SETS,
            CollectionName.STORY_STEPS
        ]

        for collection_name in required_collections:
            if collection_name not in existing_collections:
                await db.create_collection(collection_name)
                logger.info(f"Created required collection: {collection_name}")
            else:
                logger.info(f"Required collection {collection_name} already exists")

        logger.info("V2 required collections verified successfully")

    except Exception as e:
        logger.error(f"Error verifying V2 collections: {str(e)}")
        raise


async def create_v2_indexes(db: Database) -> None:
    """
    Create indexes for V2 collections.
    
    Args:
        db: The MongoDB database
    """
    try:
        # Task collections indexes
        logger.info("Creating task_collections indexes...")
        await db[CollectionName.TASK_COLLECTIONS].create_index("collection_id", unique=True)
        await db[CollectionName.TASK_COLLECTIONS].create_index("user_id")
        await db[CollectionName.TASK_COLLECTIONS].create_index("session_id")
        await db[CollectionName.TASK_COLLECTIONS].create_index("created_at")
        await db[CollectionName.TASK_COLLECTIONS].create_index([("user_id", 1), ("created_at", -1)])
        await db[CollectionName.TASK_COLLECTIONS].create_index("status")
        await db[CollectionName.TASK_COLLECTIONS].create_index("optimized_for_performance")
        
        # Story collections indexes
        logger.info("Creating story_collections indexes...")
        await db[CollectionName.STORY_COLLECTIONS].create_index("collection_id", unique=True)
        await db[CollectionName.STORY_COLLECTIONS].create_index("user_id")
        await db[CollectionName.STORY_COLLECTIONS].create_index("session_id")
        await db[CollectionName.STORY_COLLECTIONS].create_index("created_at")
        await db[CollectionName.STORY_COLLECTIONS].create_index([("user_id", 1), ("created_at", -1)])
        await db[CollectionName.STORY_COLLECTIONS].create_index("status")
        
        # Update existing story_steps collection to support collection_id
        logger.info("Updating story_steps indexes for V2 compatibility...")
        await db[CollectionName.STORY_STEPS].create_index("collection_id")
        
        # Update existing task_sets collection to support collection_id
        logger.info("Updating task_sets indexes for V2 compatibility...")
        await db[CollectionName.TASK_SETS].create_index("collection_id")
        
        logger.info("V2 indexes created successfully")
        
    except Exception as e:
        logger.error(f"Error creating V2 indexes: {str(e)}")
        raise


async def migrate_v2_collections(db: Database) -> None:
    """
    Run the complete V2 migration.
    
    Args:
        db: The MongoDB database
    """
    try:
        logger.info("Starting V2 collections migration...")
        
        # Ensure existing collections
        await ensure_existing_collections(db)
        
        # Create indexes
        await create_v2_indexes(db)
        
        logger.info("V2 collections migration completed successfully")
        
    except Exception as e:
        logger.error(f"V2 migration failed: {str(e)}")
        raise


async def verify_v2_migration(db: Database) -> bool:
    """
    Verify that the V2 migration was successful.
    
    Args:
        db: The MongoDB database
        
    Returns:
        True if migration is verified, False otherwise
    """
    try:
        logger.info("Verifying V2 migration...")
        
        # Check collections exist
        existing_collections = await db.list_collection_names()
        
        required_collections = [
            CollectionName.TASK_COLLECTIONS,
            CollectionName.STORY_COLLECTIONS
        ]
        
        for collection_name in required_collections:
            if collection_name not in existing_collections:
                logger.error(f"Required collection {collection_name} not found")
                return False
        
        # Check indexes exist
        task_collections_indexes = await db[CollectionName.TASK_COLLECTIONS].list_indexes().to_list(length=None)
        story_collections_indexes = await db[CollectionName.STORY_COLLECTIONS].list_indexes().to_list(length=None)
        
        # Verify key indexes exist
        task_index_names = [idx['name'] for idx in task_collections_indexes]
        story_index_names = [idx['name'] for idx in story_collections_indexes]
        
        required_task_indexes = ['collection_id_1', 'user_id_1', 'session_id_1']
        required_story_indexes = ['collection_id_1', 'user_id_1', 'session_id_1']
        
        for index_name in required_task_indexes:
            if index_name not in task_index_names:
                logger.error(f"Required task collection index {index_name} not found")
                return False
        
        for index_name in required_story_indexes:
            if index_name not in story_index_names:
                logger.error(f"Required story collection index {index_name} not found")
                return False
        
        logger.info("V2 migration verification successful")
        return True
        
    except Exception as e:
        logger.error(f"V2 migration verification failed: {str(e)}")
        return False


async def rollback_v2_migration(db: Database) -> None:
    """
    Rollback the V2 migration (for development/testing purposes).
    
    WARNING: This will drop the V2 collections and all their data!
    
    Args:
        db: The MongoDB database
    """
    try:
        logger.warning("Rolling back V2 migration - THIS WILL DELETE DATA!")
        
        # Drop V2 collections
        collections_to_drop = [
            CollectionName.TASK_COLLECTIONS,
            CollectionName.STORY_COLLECTIONS
        ]
        
        for collection_name in collections_to_drop:
            try:
                await db.drop_collection(collection_name)
                logger.info(f"Dropped collection: {collection_name}")
            except Exception as e:
                logger.warning(f"Could not drop collection {collection_name}: {e}")
        
        # Remove V2-specific indexes from existing collections
        try:
            await db[CollectionName.STORY_STEPS].drop_index("collection_id_1")
            await db[CollectionName.TASK_SETS].drop_index("collection_id_1")
            logger.info("Removed V2 indexes from existing collections")
        except Exception as e:
            logger.warning(f"Could not remove V2 indexes: {e}")
        
        logger.info("V2 migration rollback completed")
        
    except Exception as e:
        logger.error(f"V2 migration rollback failed: {str(e)}")
        raise


if __name__ == "__main__":
    """
    Run migration directly for testing purposes.
    """
    import os
    from app.shared.database import get_database_manager
    
    async def run_migration():
        # Get database manager
        db_manager = get_database_manager()
        await db_manager.initialize()
        
        # Get admin database for migration
        async with db_manager.get_admin_db() as admin_db:
            # Run migration
            await migrate_v2_collections(admin_db)
            
            # Verify migration
            success = await verify_v2_migration(admin_db)
            if success:
                print("✅ V2 migration completed and verified successfully")
            else:
                print("❌ V2 migration verification failed")
        
        await db_manager.close()
    
    # Run the migration
    asyncio.run(run_migration())
