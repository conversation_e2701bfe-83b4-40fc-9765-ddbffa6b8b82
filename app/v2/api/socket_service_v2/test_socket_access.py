#!/usr/bin/env python3
"""
Test script to verify Socket.IO V2 accessibility

This script tests:
1. HTTP API endpoints accessibility at /v2/*
2. Socket.IO WebSocket connectivity at /v2/socket.io
3. Authentication flow for V2
"""

import asyncio
import aiohttp
import socketio
import json
from typing import Dict, Any


async def test_v2_http_endpoints():
    """Test V2 HTTP endpoints accessibility."""
    print("🧪 Testing V2 HTTP endpoints...")
    
    base_url = "http://localhost:8204"  # Traefik port
    
    endpoints_to_test = [
        "/v2/",
        "/v2/health",
        "/v2/status",
        "/v2/docs"
    ]
    
    async with aiohttp.ClientSession() as session:
        for endpoint in endpoints_to_test:
            try:
                url = f"{base_url}{endpoint}"
                async with session.get(url) as response:
                    status = response.status
                    if status == 200:
                        print(f"✅ {endpoint} - Status: {status}")
                    else:
                        print(f"⚠️  {endpoint} - Status: {status}")
            except Exception as e:
                print(f"❌ {endpoint} - Error: {e}")


async def test_v2_socket_connection():
    """Test Socket.IO V2 WebSocket connection."""
    print("\n🔌 Testing V2 Socket.IO connection...")
    
    # Create Socket.IO client
    sio = socketio.AsyncClient()
    
    # Connection events
    @sio.event
    async def connect():
        print("✅ Connected to Socket.IO V2")
        await sio.emit('test_event', {'message': 'Hello from V2 test'})
    
    @sio.event
    async def disconnect():
        print("🔌 Disconnected from Socket.IO V2")
    
    @sio.event
    async def connect_error(data):
        print(f"❌ Connection error: {data}")
    
    try:
        # Connect to V2 Socket.IO endpoint
        await sio.connect('http://localhost:8204/v2/socket.io')
        
        # Wait a bit for connection to establish
        await asyncio.sleep(2)
        
        # Disconnect
        await sio.disconnect()
        
    except Exception as e:
        print(f"❌ Socket.IO connection failed: {e}")


async def test_v2_authentication_flow():
    """Test V2 authentication and session creation."""
    print("\n🔐 Testing V2 authentication flow...")
    
    base_url = "http://localhost:8204"
    
    # Mock JWT token (in real scenario, get this from auth service)
    mock_jwt = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6InRlc3RfdXNlciIsInRlbmFudF9pZCI6InRlc3RfdGVuYW50In0.test"
    
    async with aiohttp.ClientSession() as session:
        try:
            # Test connect endpoint
            headers = {
                'Authorization': f'Bearer {mock_jwt}',
                'Content-Type': 'application/json'
            }
            
            async with session.post(f"{base_url}/v2/connect", headers=headers) as response:
                status = response.status
                if status == 200:
                    data = await response.json()
                    print(f"✅ Authentication successful")
                    print(f"   Session ID: {data.get('session_id', 'N/A')}")
                    print(f"   WebSocket URL: {data.get('websocket_url', 'N/A')}")
                    print(f"   Service Version: {data.get('service_version', 'N/A')}")
                    return data
                else:
                    print(f"⚠️  Authentication failed - Status: {status}")
                    text = await response.text()
                    print(f"   Response: {text}")
                    
        except Exception as e:
            print(f"❌ Authentication test failed: {e}")
    
    return None


async def test_v2_socket_with_auth():
    """Test Socket.IO V2 connection with authentication."""
    print("\n🔐🔌 Testing V2 Socket.IO with authentication...")
    
    # First get session token
    auth_data = await test_v2_authentication_flow()
    if not auth_data:
        print("❌ Cannot test Socket.IO with auth - authentication failed")
        return
    
    session_token = auth_data.get('session_token')
    websocket_url = auth_data.get('websocket_url', '/v2/socket.io')
    
    # Create Socket.IO client with auth
    sio = socketio.AsyncClient()
    
    @sio.event
    async def connect():
        print("✅ Authenticated Socket.IO V2 connection successful")
    
    @sio.event
    async def connect_error(data):
        print(f"❌ Authenticated connection error: {data}")
    
    try:
        # Connect with authentication
        await sio.connect(
            f'http://localhost:8204{websocket_url}',
            auth={'session_token': session_token}
        )
        
        await asyncio.sleep(2)
        await sio.disconnect()
        
    except Exception as e:
        print(f"❌ Authenticated Socket.IO connection failed: {e}")


async def main():
    """Run all V2 accessibility tests."""
    print("🚀 Starting Socket Service V2 Accessibility Tests")
    print("=" * 60)
    
    # Test HTTP endpoints
    await test_v2_http_endpoints()
    
    # Test basic Socket.IO connection
    await test_v2_socket_connection()
    
    # Test authentication flow
    await test_v2_authentication_flow()
    
    # Test Socket.IO with authentication
    await test_v2_socket_with_auth()
    
    print("\n" + "=" * 60)
    print("🏁 V2 accessibility tests completed")


if __name__ == "__main__":
    asyncio.run(main())
