"""
Socket.IO Authentication and Connection Management for Service V2.

Handles secure Socket.IO connection establishment with proper authentication
and session management for the collection-based architecture.
"""

import uuid
from typing import Dict, Any, Optional
from datetime import datetime, timezone, timedelta

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from app.shared.utils.logger import setup_new_logging
from app.shared.security import get_tenant_info

# Configure logging
logger = setup_new_logging(__name__)

# Create router
router = APIRouter(prefix="", tags=["Socket.IO Authentication V2"])

# In-memory session store (in production, use Redis)
active_sessions: Dict[str, Dict[str, Any]] = {}


class SocketConnectionResponse(BaseModel):
    """Socket.IO connection response model for V2."""
    session_token: str
    session_id: str
    websocket_url: str
    expires_at: str
    status: str = "ready"
    service_version: str = "v2"
    instructions: Dict[str, Any]


class SessionStatusUpdate(BaseModel):
    """Session status update model."""
    session_id: str
    status: str  # STARTED, ACTIVE, CANCELLED, COMPLETED
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None


@router.post("/connect", response_model=SocketConnectionResponse)
async def create_socket_session(
    tenant_info = Depends(get_tenant_info)
):
    """
    Create authenticated Socket.IO session for Service V2.

    This endpoint:
    1. Validates user authentication (via JWT token)
    2. Creates a secure session token
    3. Returns WebSocket connection details for collection-based processing
    4. Prepares for audio streaming with V2 optimizations

    Flow: HTTP Auth → Session Token → WebSocket Connection → Collection IDs
    """
    try:
        # Extract user information from authenticated token
        user_id = tenant_info.user.username
        tenant_id = tenant_info.tenant_id

        # Generate session credentials
        session_id = f"session_v2_{uuid.uuid4().hex[:16]}"
        session_token = f"token_v2_{uuid.uuid4().hex}"

        # Create session data with complete user context
        session_data = {
            "session_id": session_id,
            "session_token": session_token,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "service_version": "v2",
            "created_at": datetime.now(timezone.utc),
            "expires_at": datetime.now(timezone.utc) + timedelta(hours=2),
            "status": "created",
            "metadata": {
                "collection_based": True,
                "performance_optimized": True
            },
            "current_user": tenant_info  # Store complete UserTenantDB context
        }
        logger.info(f"Created V2 session data: {session_data}")

        # Store session in memory (in production, use Redis)
        active_sessions[session_token] = session_data
        logger.info(f"✅ Created socket session V2 {session_id} for user {user_id}")

        return SocketConnectionResponse(
            session_token=session_token,
            session_id=session_id,
            websocket_url="/v1/socket/socket.io",  # Same endpoint, different processing
            expires_at=session_data["expires_at"].isoformat(),
            status="ready",
            service_version="v2",
            instructions={
                "next_step": "Connect to WebSocket using session_token",
                "websocket_endpoint": "/v1/socket/socket.io",
                "auth_method": "Include session_token in auth object",
                "service_version": "v2",
                "response_format": "Collection-based (task_set_id, story_set_id)",
                "optimizations": [
                    "Choice questions exclude media for performance",
                    "Interactive tasks include media",
                    "Collection-based storage"
                ],
                "flow": {
                    "1": "Send 'stream_starting' event to begin",
                    "2": "Wait for 'stream_starting_ack' response",
                    "3": "Send binary audio chunks",
                    "4": "Send 'stream_completed' or 'stream_stop' to finish",
                    "5": "Receive task_set_id and story_set_id in response"
                },
                "events": {
                    "stream_starting": "Begin audio streaming session",
                    "binary_data": "Send audio chunks for processing",
                    "stream_completed": "Signal normal completion",
                    "stream_stop": "Signal forced stop"
                },
                "expected_response": {
                    "task_set_id": "Collection ID for generated tasks",
                    "story_set_id": "Collection ID for generated stories",
                    "session_id": "Session identifier",
                    "optimization_stats": "Performance optimization metadata"
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create socket session V2: {e}")
        raise HTTPException(status_code=500, detail="Failed to create V2 session")


@router.get("/validate/{session_token}")
async def validate_socket_session(session_token: str):
    """
    Validate Socket.IO session token for V2.

    Used internally by Socket.IO server to validate connections.
    """
    try:
        session_data = active_sessions.get(session_token)

        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")

        # Check expiration
        if datetime.now(timezone.utc) > session_data["expires_at"]:
            # Clean up expired session
            del active_sessions[session_token]
            raise HTTPException(status_code=401, detail="Session expired")

        # Return session info for Socket.IO server
        return {
            "valid": True,
            "session_id": session_data["session_id"],
            "user_id": session_data["user_id"],
            "tenant_id": session_data["tenant_id"],
            "service_version": session_data.get("service_version", "v2")
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to validate V2 session: {e}")
        raise HTTPException(status_code=500, detail="V2 session validation failed")


@router.delete("/session/{session_token}")
async def cleanup_socket_session(session_token: str):
    """
    Clean up Socket.IO session for V2.

    Used when a session is completed or cancelled.
    """
    try:
        if session_token in active_sessions:
            session_data = active_sessions[session_token]
            del active_sessions[session_token]
            logger.info(f"✅ Cleaned up V2 session {session_data.get('session_id', 'unknown')}")
            return {
                "status": "cleaned_up", 
                "session_id": session_data.get("session_id"),
                "service_version": "v2"
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cleanup V2 session: {e}")
        raise HTTPException(status_code=500, detail="V2 session cleanup failed")


@router.get("/status")
async def get_socket_status():
    """
    Get Socket.IO connection status and statistics for V2.

    Useful for monitoring and debugging collection-based processing.
    """
    try:
        current_time = datetime.now(timezone.utc)

        # Count active sessions
        active_count = 0
        expired_count = 0
        v2_sessions = 0

        for session_data in active_sessions.values():
            if current_time <= session_data["expires_at"]:
                active_count += 1
                if session_data.get("service_version") == "v2":
                    v2_sessions += 1
            else:
                expired_count += 1

        return {
            "service_version": "v2",
            "active_sessions": active_count,
            "v2_sessions": v2_sessions,
            "expired_sessions": expired_count,
            "total_sessions": len(active_sessions),
            "features": {
                "collection_based_storage": True,
                "performance_optimized": True,
                "media_optimization": True
            },
            "timestamp": current_time.isoformat()
        }

    except Exception as e:
        logger.error(f"Failed to get V2 socket status: {e}")
        raise HTTPException(status_code=500, detail="V2 status check failed")


@router.put("/session/{session_token}/status")
async def update_session_status(
    session_token: str,
    status_update: SessionStatusUpdate
):
    """
    Update Socket.IO session status for V2.

    Used by Socket.IO server to track session lifecycle.
    """
    try:
        session_data = active_sessions.get(session_token)

        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")

        # Check expiration
        if datetime.now(timezone.utc) > session_data["expires_at"]:
            del active_sessions[session_token]
            raise HTTPException(status_code=401, detail="Session expired")

        # Update session status
        session_data["status"] = status_update.status
        session_data["last_status_update"] = datetime.now(timezone.utc).isoformat()

        if status_update.metadata:
            session_data["metadata"] = {
                **session_data.get("metadata", {}),
                **status_update.metadata
            }

        logger.info(f"Updated V2 session {session_data['session_id']} status to {status_update.status}")
        return {
            "status": "updated",
            "session_id": session_data["session_id"],
            "new_status": status_update.status,
            "service_version": "v2"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update V2 session status: {e}")
        raise HTTPException(status_code=500, detail="V2 status update failed")


def get_session_by_token(session_token: str) -> Optional[Dict[str, Any]]:
    """
    Get session data by token for V2.

    Used internally by Socket.IO server.
    """
    return active_sessions.get(session_token)
