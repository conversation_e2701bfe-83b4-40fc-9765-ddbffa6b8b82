"""
Socket Service V2 - Collection-Based Task and Story Generation

This service follows the same flow as the original socket service but implements
collection-based storage for tasks and stories, returning task_set_id and story_set_id
instead of individual items.

Key Features:
- Same Socket.IO communication flow as original service
- Collection-based storage for tasks and stories
- Performance optimized task generation (no media for choice questions)
- Returns task_set_id and story_set_id for collections
- Maintains compatibility with existing client implementations

Architecture:
- FastAPI application with Socket.IO integration
- Redis-backed session management and queue processing
- MongoDB collections for task_collections and story_collections
- Background audio processing with Gemini AI
"""

import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.shared.utils.logger import setup_new_logging
from app.shared.socketio.socketio_server_v2 import SocketIOServerV2
from app.shared.redis.redis_manager import RedisManager
from app.shared.redis.queue.queue_worker_v2 import QueueWorkerV2

# Import routes
from app.v1.api.socket_service_v2.routes.socket_auth import router as socket_auth_router
from app.v1.api.socket_service_v2.routes.audio import router as audio_router
from app.v1.api.socket_service_v2.routes.story import router as story_router

# Configure logging
logger = setup_new_logging(__name__)

# Global instances
redis_manager = None
socketio_server = None
queue_worker = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan with proper startup and shutdown."""
    global redis_manager, socketio_server, queue_worker

    try:
        logger.info("🚀 Starting Socket Service V2...")

        # Initialize Redis
        import os
        redis_url = os.getenv("REDIS_URL", "redis://redis:6379")
        redis_manager = RedisManager(redis_url=redis_url)
        await redis_manager.ensure_connected()
        logger.info("✅ Redis connection established")

        # Initialize Socket.IO server V2
        socketio_server = SocketIOServerV2(redis_manager)
        await socketio_server.setup()
        logger.info("✅ Socket.IO server V2 initialized")

        # Initialize Queue Worker V2
        queue_worker = QueueWorkerV2(
            audio_queue_manager=socketio_server.audio_queue_manager,
            socketio_server=socketio_server
        )
        
        # Start queue worker in background
        asyncio.create_task(queue_worker.start())
        logger.info("✅ Queue worker V2 started")

        yield

    except Exception as e:
        logger.error(f"❌ Failed to start Socket Service V2: {e}")
        raise
    finally:
        logger.info("🛑 Shutting down Socket Service V2...")
        
        # Stop queue worker
        if queue_worker:
            await queue_worker.stop()
            logger.info("✅ Queue worker V2 stopped")
        
        # Close Redis connections
        if redis_manager:
            await redis_manager.close()
            logger.info("✅ Redis connections closed")


# Create FastAPI app
app = FastAPI(
    title="Socket Service V2",
    description="Collection-based Socket.IO service for real-time audio processing and task generation",
    version="2.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount Socket.IO app
@app.on_event("startup")
async def mount_socketio():
    """Mount Socket.IO app after startup."""
    global socketio_server
    if socketio_server:
        app.mount("/socket.io", socketio_server.app)
        logger.info("✅ Socket.IO app mounted at /socket.io")

# Include routers
app.include_router(socket_auth_router, tags=["Socket.IO Authentication V2"])
app.include_router(audio_router, tags=["Audio Processing V2"])
app.include_router(story_router, tags=["Story Generation V2"])


@app.get("/")
async def root():
    """Root endpoint for Socket Service V2."""
    return {
        "service": "Socket Service V2",
        "version": "2.0.0",
        "description": "Collection-based Socket.IO service for real-time audio processing",
        "features": [
            "Collection-based task and story storage",
            "Performance optimized task generation",
            "Same Socket.IO communication flow",
            "Returns task_set_id and story_set_id"
        ]
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    global redis_manager, socketio_server, queue_worker
    
    health_status = {
        "service": "Socket Service V2",
        "status": "healthy",
        "components": {}
    }
    
    # Check Redis
    if redis_manager:
        try:
            await redis_manager.ping()
            health_status["components"]["redis"] = "healthy"
        except Exception as e:
            health_status["components"]["redis"] = f"unhealthy: {e}"
            health_status["status"] = "degraded"
    else:
        health_status["components"]["redis"] = "not_initialized"
        health_status["status"] = "degraded"
    
    # Check Queue Worker
    if queue_worker:
        worker_health = await queue_worker.health_check()
        health_status["components"]["queue_worker"] = worker_health
        if not worker_health.get("is_running", False):
            health_status["status"] = "degraded"
    else:
        health_status["components"]["queue_worker"] = "not_initialized"
        health_status["status"] = "degraded"
    
    return health_status


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)
