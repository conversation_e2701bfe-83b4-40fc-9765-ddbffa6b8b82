"""
Database initialization utilities for MongoDB.
"""
import logging
from pymongo.database import Database
from pymongo import AsyncMongoClient
from app.shared.db_enums import CollectionName

logger = logging.getLogger(__name__)

async def ensure_indexes(db: Database) -> None:
    """
    Ensure that all required indexes exist in the database.
    
    Args:
        db: The MongoDB database
    """
    try:
        # Task sets indexes
        await db[CollectionName.TASK_SETS].create_index("user_id")
        await db[CollectionName.TASK_SETS].create_index("created_at")
        await db[CollectionName.TASK_SETS].create_index("status")
        await db[CollectionName.TASK_SETS].create_index([("user_id", 1), ("created_at", -1)])
        
        # Task items indexes
        await db[CollectionName.TASK_ITEMS].create_index("type")
        await db[CollectionName.TASK_ITEMS].create_index("status")
        
        # Task history indexes
        await db[CollectionName.TASK_HISTORY].create_index("user_id")
        await db[CollectionName.TASK_HISTORY].create_index("task_set_id")
        await db[CollectionName.TASK_HISTORY].create_index("submitted_at")
        
        # User scores indexes
        await db[CollectionName.USER_SCORES].create_index("user_id", unique=True)
        await db[CollectionName.USER_SCORES].create_index("score")
        
        logger.info("MongoDB indexes created successfully")
    except Exception as e:
        logger.error(f"Error creating MongoDB indexes: {str(e)}")

async def ensure_collections(db: Database) -> None:
    """
    Ensure that all required collections exist in the database.
    
    Args:
        db: The MongoDB database
    """
    try:
        # Get existing collections
        existing_collections = await db.list_collection_names()
        
        # Create collections if they don't exist
        for collection_name in CollectionName:
            if collection_name not in existing_collections:
                await db.create_collection(collection_name)
                logger.info(f"Created collection: {collection_name}")
        
        # Create indexes
        await ensure_indexes(db)
        
        logger.info("MongoDB collections initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing MongoDB collections: {str(e)}")
