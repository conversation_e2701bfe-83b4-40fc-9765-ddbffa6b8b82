# Simplified Traefik Dynamic Configuration
# Minimal configuration for optimal performance and reduced latency
#
# Root Cause Documentation:
# Issue: Traefik middleware configuration from Docker Compose labels not being applied to service routers
# Solution: Force recreate containers to refresh Traefik's router configuration
# Prevention: This issue may occur when Traefik configuration changes or containers are restarted in certain orders

http:
  middlewares:
    # Permissive CORS middleware - allow everything (nginx will handle security)
    cors:
      headers:
        accessControlAllowMethods:
          - "*"
        accessControlAllowHeaders:
          - "*"
        accessControlAllowOriginList:
          - "*"
        accessControlAllowCredentials: true
        accessControlMaxAge: 86400
        addVaryHeader: true



    # Lightweight compression for better performance
    compress:
      compress: {}

    # Basic auth for Traefik dashboard
    dashboard-auth:
      basicAuth:
        users:
          - "admin:$apr1$H6uskkkW$IgXLP6ewTrSuBkTrqE8wj/"

    # Path stripping middleware for clean service routing
    strip-auth-prefix:
      stripPrefix:
        prefixes:
          - "/v1/auth"

    strip-socket-prefix:
      stripPrefix:
        prefixes:
          - "/v1/socket"

    strip-management-prefix:
      stripPrefix:
        prefixes:
          - "/v1/management"

    # Socket.IO sticky sessions middleware for scaling
    socket-sticky:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: "http"
          X-Forwarded-Port: "8204"
          # Enable WebSocket support
          Connection: "upgrade"
          Upgrade: "websocket"

    # File upload size limit middleware for audio endpoints (200MB)
    audio-upload-limit:
      buffering:
        maxRequestBodyBytes: *********  # 200MB in bytes
        memRequestBodyBytes: *********  # 200MB in bytes
        maxResponseBodyBytes: *********  # 200MB in bytes
        memResponseBodyBytes: *********  # 200MB in bytes
        retryExpression: "IsNetworkError() && Attempts() <= 2"



  routers:

  services:




