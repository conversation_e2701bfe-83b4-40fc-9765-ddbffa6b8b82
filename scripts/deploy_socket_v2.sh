#!/bin/bash

# Socket Service V2 Deployment Script
# This script deploys Socket Service V2 with proper accessibility at /v2

set -e

echo "🚀 Deploying Socket Service V2..."
echo "=================================="

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not installed or not in PATH"
    exit 1
fi

# Set Docker Compose command
if docker compose version &> /dev/null; then
    COMPOSE_CMD="docker compose"
else
    COMPOSE_CMD="docker-compose"
fi

echo "✅ Docker and Docker Compose are available"

# Build and deploy Socket Service V2
echo ""
echo "📦 Building Socket Service V2..."
$COMPOSE_CMD build socket_v2

echo ""
echo "🔄 Starting Socket Service V2..."
$COMPOSE_CMD up -d socket_v2

# Wait for service to be ready
echo ""
echo "⏳ Waiting for Socket Service V2 to be ready..."
sleep 10

# Check service health
echo ""
echo "🏥 Checking Socket Service V2 health..."

# Get the API port from environment or use default
API_PORT=${API_PORT:-8204}

# Test health endpoint
if curl -f "http://localhost:${API_PORT}/v2/health" > /dev/null 2>&1; then
    echo "✅ Socket Service V2 health check passed"
else
    echo "❌ Socket Service V2 health check failed"
    echo "📋 Checking service logs..."
    $COMPOSE_CMD logs --tail=20 socket_v2
    exit 1
fi

# Test basic endpoints
echo ""
echo "🧪 Testing V2 endpoints accessibility..."

endpoints=(
    "/v2/"
    "/v2/health"
    "/v2/status"
)

for endpoint in "${endpoints[@]}"; do
    if curl -f "http://localhost:${API_PORT}${endpoint}" > /dev/null 2>&1; then
        echo "✅ ${endpoint} - accessible"
    else
        echo "⚠️  ${endpoint} - not accessible"
    fi
done

# Test Socket.IO endpoint (basic connectivity)
echo ""
echo "🔌 Testing Socket.IO V2 endpoint..."
if curl -f "http://localhost:${API_PORT}/v2/socket.io/" > /dev/null 2>&1; then
    echo "✅ Socket.IO V2 endpoint - accessible"
else
    echo "⚠️  Socket.IO V2 endpoint - may not be accessible (this could be normal)"
fi

# Show service status
echo ""
echo "📊 Socket Service V2 Status:"
$COMPOSE_CMD ps socket_v2

# Show recent logs
echo ""
echo "📋 Recent Socket Service V2 logs:"
$COMPOSE_CMD logs --tail=10 socket_v2

echo ""
echo "🎉 Socket Service V2 deployment completed!"
echo ""
echo "📡 Service accessible at:"
echo "   - API: http://localhost:${API_PORT}/v2/"
echo "   - Health: http://localhost:${API_PORT}/v2/health"
echo "   - Socket.IO: http://localhost:${API_PORT}/v2/socket.io"
echo "   - Docs: http://localhost:${API_PORT}/v2/docs"
echo ""
echo "🔧 To test the service:"
echo "   python app/v2/api/socket_service_v2/test_socket_access.py"
echo ""
echo "📋 To view logs:"
echo "   $COMPOSE_CMD logs -f socket_v2"
echo ""
echo "🛑 To stop the service:"
echo "   $COMPOSE_CMD stop socket_v2"
